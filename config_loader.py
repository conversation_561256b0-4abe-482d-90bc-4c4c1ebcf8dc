"""
Configuration loader for the Index Scanner application.
Loads configuration from config.yaml and provides centralized access to all settings.
"""

import yaml
import os
import logging
from typing import Dict, Any, List, Optional

logger = logging.getLogger(__name__)

class ConfigLoader:
    """Centralized configuration loader for the Index Scanner application."""
    
    def __init__(self, config_path: str = "config.yaml"):
        """
        Initialize the configuration loader.
        
        Args:
            config_path: Path to the config.yaml file
        """
        self.config_path = config_path
        self._config = None
        self.load_config()
    
    def load_config(self) -> None:
        """Load configuration from YAML file."""
        try:
            if not os.path.exists(self.config_path):
                raise FileNotFoundError(f"Configuration file not found: {self.config_path}")
            
            with open(self.config_path, 'r', encoding='utf-8') as file:
                self._config = yaml.safe_load(file)
            
            logger.info(f"Configuration loaded successfully from {self.config_path}")
            
        except Exception as e:
            logger.error(f"Failed to load configuration: {e}")
            raise
    
    def get(self, key_path: str, default: Any = None) -> Any:
        """
        Get configuration value using dot notation.
        
        Args:
            key_path: Dot-separated path to the configuration value (e.g., 'general.env_path')
            default: Default value if key is not found
            
        Returns:
            Configuration value or default
        """
        if self._config is None:
            return default
        
        keys = key_path.split('.')
        value = self._config
        
        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default
    
    # General Settings
    @property
    def env_path(self) -> str:
        """Get environment file path."""
        return self.get('general.env_path', 'C:/Users/<USER>/Desktop/Python/.env')
    
    @property
    def output_dir(self) -> str:
        """Get output directory for reports."""
        return self.get('general.output_dir', 'reports')
    
    # Trading Symbols
    @property
    def symbols(self) -> List[str]:
        """Get list of trading symbols."""
        return self.get('symbols', ['NIFTY', 'BANKNIFTY'])
    
    # Options Filter Settings
    @property
    def min_volume(self) -> int:
        """Get minimum trading volume filter."""
        return self.get('options_filter.min_volume', 5)

    @property
    def max_volume(self) -> int:
        """Get maximum trading volume filter."""
        return self.get('options_filter.max_volume', 1000000)

    @property
    def min_ltp_price(self) -> float:
        """Get minimum LTP price filter."""
        return self.get('options_filter.min_ltp_price', 2500.0)

    @property
    def max_ltp_price(self) -> float:
        """Get maximum LTP price filter."""
        return self.get('options_filter.max_ltp_price', 5000.0)

    # EMA Indicator Settings
    @property
    def ema_enabled(self) -> bool:
        """Get whether EMA indicator is enabled."""
        return self.get('ema_indicator.enabled', True)

    @property
    def ema_short_period(self) -> int:
        """Get EMA short period."""
        return self.get('ema_indicator.short_period', 9)

    @property
    def ema_long_period(self) -> int:
        """Get EMA long period."""
        return self.get('ema_indicator.long_period', 21)
    
    def validate_config(self) -> bool:
        """
        Validate the loaded configuration.
        
        Returns:
            True if configuration is valid, False otherwise
        """
        try:
            # Check required sections exist
            required_sections = ['general', 'symbols', 'options_filter']
            
            for section in required_sections:
                if section not in self._config:
                    logger.error(f"Missing required configuration section: {section}")
                    return False
            
            # Validate symbols list is not empty
            if not self.symbols:
                logger.error("No symbols configured")
                return False
            
            # Validate positive values
            if self.min_volume <= 0:
                logger.error("min_volume must be positive")
                return False
            
            if self.min_ltp_price <= 0:
                logger.error("min_ltp_price must be positive")
                return False
                
            if self.max_ltp_price <= 0:
                logger.error("max_ltp_price must be positive")
                return False
                
            if self.min_ltp_price >= self.max_ltp_price:
                logger.error("min_ltp_price must be less than max_ltp_price")
                return False
            
            logger.info("Configuration validation successful")
            return True
            
        except Exception as e:
            logger.error(f"Configuration validation failed: {e}")
            return False

# Global configuration instance
_config_instance = None

def get_config(config_path: str = "config.yaml") -> ConfigLoader:
    """
    Get the global configuration instance.
    
    Args:
        config_path: Path to the config.yaml file
        
    Returns:
        ConfigLoader instance
    """
    global _config_instance
    if _config_instance is None:
        # Use absolute path if relative path is provided
        if not os.path.isabs(config_path):
            config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), config_path)
        _config_instance = ConfigLoader(config_path)
    return _config_instance

def reload_config(config_path: str = "config.yaml") -> ConfigLoader:
    """
    Reload the configuration.
    
    Args:
        config_path: Path to the config.yaml file
        
    Returns:
        New ConfigLoader instance
    """
    global _config_instance
    if not os.path.isabs(config_path):
        config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), config_path)
    _config_instance = ConfigLoader(config_path)
    return _config_instance
