"""
Technical indicators for market analysis.
Contains implementations of various technical indicators like EMA, SMA, etc.
"""

import logging
from typing import List, Dict, Optional
import math

logger = logging.getLogger(__name__)

class TechnicalIndicators:
    """Class containing various technical indicator calculations."""
    
    @staticmethod
    def calculate_ema(prices: List[float], period: int) -> List[float]:
        """
        Calculate Exponential Moving Average (EMA).
        
        Args:
            prices: List of price values
            period: Period for EMA calculation
            
        Returns:
            List of EMA values (same length as input, with NaN for initial values)
        """
        if not prices or period <= 0:
            return []
        
        if len(prices) < period:
            return [float('nan')] * len(prices)
        
        ema_values = []
        multiplier = 2 / (period + 1)
        
        # Calculate initial SMA for the first EMA value
        sma = sum(prices[:period]) / period
        ema_values.extend([float('nan')] * (period - 1))
        ema_values.append(sma)
        
        # Calculate EMA for remaining values
        for i in range(period, len(prices)):
            ema = (prices[i] * multiplier) + (ema_values[-1] * (1 - multiplier))
            ema_values.append(ema)
        
        return ema_values
    
    @staticmethod
    def calculate_sma(prices: List[float], period: int) -> List[float]:
        """
        Calculate Simple Moving Average (SMA).
        
        Args:
            prices: List of price values
            period: Period for SMA calculation
            
        Returns:
            List of SMA values (same length as input, with NaN for initial values)
        """
        if not prices or period <= 0:
            return []
        
        if len(prices) < period:
            return [float('nan')] * len(prices)
        
        sma_values = []
        
        # Fill initial values with NaN
        sma_values.extend([float('nan')] * (period - 1))
        
        # Calculate SMA for each valid window
        for i in range(period - 1, len(prices)):
            window = prices[i - period + 1:i + 1]
            sma = sum(window) / period
            sma_values.append(sma)
        
        return sma_values
    
    @staticmethod
    def get_ema_signal(short_ema: List[float], long_ema: List[float]) -> str:
        """
        Get EMA signal based on short and long EMA crossover.
        
        Args:
            short_ema: Short period EMA values
            long_ema: Long period EMA values
            
        Returns:
            Signal string: 'BUY', 'SELL', or 'NEUTRAL'
        """
        if not short_ema or not long_ema or len(short_ema) < 2 or len(long_ema) < 2:
            return 'NEUTRAL'
        
        # Get the last two values for both EMAs
        short_current = short_ema[-1]
        short_previous = short_ema[-2]
        long_current = long_ema[-1]
        long_previous = long_ema[-2]
        
        # Check for NaN values
        if (math.isnan(short_current) or math.isnan(short_previous) or 
            math.isnan(long_current) or math.isnan(long_previous)):
            return 'NEUTRAL'
        
        # Check for bullish crossover (short EMA crosses above long EMA)
        if short_previous <= long_previous and short_current > long_current:
            return 'BUY'
        
        # Check for bearish crossover (short EMA crosses below long EMA)
        if short_previous >= long_previous and short_current < long_current:
            return 'SELL'
        
        # No crossover
        return 'NEUTRAL'
    
    @staticmethod
    def calculate_ema_for_symbol(prices: List[float], short_period: int, long_period: int) -> Dict[str, any]:
        """
        Calculate both short and long EMA for a symbol and determine signal.
        
        Args:
            prices: List of price values
            short_period: Short EMA period
            long_period: Long EMA period
            
        Returns:
            Dictionary containing EMA values and signal
        """
        try:
            short_ema = TechnicalIndicators.calculate_ema(prices, short_period)
            long_ema = TechnicalIndicators.calculate_ema(prices, long_period)
            signal = TechnicalIndicators.get_ema_signal(short_ema, long_ema)
            
            return {
                'short_ema': short_ema[-1] if short_ema and not math.isnan(short_ema[-1]) else None,
                'long_ema': long_ema[-1] if long_ema and not math.isnan(long_ema[-1]) else None,
                'signal': signal,
                'short_ema_values': short_ema,
                'long_ema_values': long_ema
            }
        except Exception as e:
            logger.error(f"Error calculating EMA: {e}")
            return {
                'short_ema': None,
                'long_ema': None,
                'signal': 'NEUTRAL',
                'short_ema_values': [],
                'long_ema_values': []
            }

class EMAAnalyzer:
    """Analyzer for EMA-based trading signals."""
    
    def __init__(self, short_period: int = 9, long_period: int = 21):
        """
        Initialize EMA analyzer.
        
        Args:
            short_period: Short EMA period
            long_period: Long EMA period
        """
        self.short_period = short_period
        self.long_period = long_period
        self.indicators = TechnicalIndicators()
    
    def analyze_symbol(self, prices: List[float]) -> Dict[str, any]:
        """
        Analyze a symbol using EMA indicators.
        
        Args:
            prices: List of historical prices
            
        Returns:
            Analysis results including EMA values and signals
        """
        return self.indicators.calculate_ema_for_symbol(
            prices, self.short_period, self.long_period
        )
    
    def get_trading_recommendation(self, analysis: Dict[str, any]) -> str:
        """
        Get trading recommendation based on EMA analysis.
        
        Args:
            analysis: EMA analysis results
            
        Returns:
            Trading recommendation string
        """
        signal = analysis.get('signal', 'NEUTRAL')
        short_ema = analysis.get('short_ema')
        long_ema = analysis.get('long_ema')
        
        if signal == 'BUY' and short_ema and long_ema:
            return f"BUY Signal: Short EMA ({short_ema:.2f}) crossed above Long EMA ({long_ema:.2f})"
        elif signal == 'SELL' and short_ema and long_ema:
            return f"SELL Signal: Short EMA ({short_ema:.2f}) crossed below Long EMA ({long_ema:.2f})"
        else:
            return "NEUTRAL: No clear EMA signal"
