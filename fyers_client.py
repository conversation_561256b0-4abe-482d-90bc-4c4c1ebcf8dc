"""
Fyers API client for fetching market data.
Handles authentication and provides methods to get quotes for symbols.
"""

import logging
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from fyers_config import FyersConfig

logger = logging.getLogger(__name__)

@dataclass
class MarketData:
    """Data class to represent market data for a symbol."""
    symbol: str
    ltp: float
    volume: int
    open_price: float
    high: float
    low: float
    close: float
    prev_close: float
    change: float
    change_percent: float

class FyersClient:
    """Client for interacting with Fyers API."""
    
    def __init__(self, env_path: str = None):
        """
        Initialize Fyers client.
        
        Args:
            env_path: Path to environment file containing Fyers credentials
        """
        self.fyers_config = FyersConfig(env_path=env_path)
        self.fyers_api = None
        self.access_token = None
        
    def authenticate(self) -> bool:
        """
        Authenticate with Fyers API.
        
        Returns:
            True if authentication successful, False otherwise
        """
        try:
            logger.info("Starting Fyers authentication...")
            self.access_token = self.fyers_config.authenticate()
            
            if self.access_token:
                # Initialize Fyers API client
                from fyers_apiv3.fyersModel import FyersModel
                
                self.fyers_api = FyersModel(
                    client_id=self.fyers_config.config["client_id"],
                    is_async=False,
                    token=self.access_token
                )
                
                logger.info("Fyers authentication successful")
                return True
            else:
                logger.error("Failed to get access token")
                return False
                
        except Exception as e:
            logger.error(f"Authentication failed: {e}")
            return False
    
    def get_quotes(self, symbols: List[str]) -> Dict[str, MarketData]:
        """
        Get market quotes for multiple symbols.
        
        Args:
            symbols: List of symbol strings in NSE format
            
        Returns:
            Dictionary mapping symbol to MarketData object
        """
        if not self.fyers_api:
            logger.error("Fyers API not initialized. Please authenticate first.")
            return {}
        
        market_data = {}
        
        try:
            # Fyers API can handle multiple symbols in one call
            # But we'll process in batches to avoid API limits
            batch_size = 50
            
            for i in range(0, len(symbols), batch_size):
                batch_symbols = symbols[i:i + batch_size]
                
                logger.info(f"Fetching quotes for batch {i//batch_size + 1}: {len(batch_symbols)} symbols")
                
                # Prepare symbols for Fyers API (comma-separated)
                symbol_string = ",".join(batch_symbols)
                
                # Get quotes from Fyers API
                response = self.fyers_api.quotes({"symbols": symbol_string})

                if response and response.get("code") == 200:
                    quotes_data = response.get("d", {})

                    # Handle case where quotes_data might be a list instead of dict
                    if isinstance(quotes_data, list):
                        # If it's a list, try to match symbols by position or find symbol field
                        for i, symbol_data in enumerate(quotes_data):
                            if i < len(batch_symbols) and isinstance(symbol_data, dict):
                                symbol = batch_symbols[i]
                                try:
                                    # Parse market data from Fyers response
                                    market_data[symbol] = self._parse_market_data(symbol, symbol_data)
                                except Exception as e:
                                    logger.warning(f"Failed to parse data for {symbol}: {e}")
                    elif isinstance(quotes_data, dict):
                        # Original logic for dict response
                        for symbol in batch_symbols:
                            symbol_data = quotes_data.get(symbol)

                            if symbol_data:
                                try:
                                    # Parse market data from Fyers response
                                    market_data[symbol] = self._parse_market_data(symbol, symbol_data)
                                except Exception as e:
                                    logger.warning(f"Failed to parse data for {symbol}: {e}")
                            else:
                                logger.warning(f"No data received for symbol: {symbol}")
                    else:
                        logger.warning(f"Unexpected quotes_data format: {type(quotes_data)}")
                else:
                    logger.error(f"Failed to get quotes for batch: {response}")
                    
        except Exception as e:
            logger.error(f"Error fetching quotes: {e}")
            
        logger.info(f"Successfully fetched market data for {len(market_data)} symbols")
        return market_data
    
    def _parse_market_data(self, symbol: str, data: Dict[str, Any]) -> MarketData:
        """
        Parse market data from Fyers API response.
        
        Args:
            symbol: Symbol string
            data: Market data from Fyers API
            
        Returns:
            MarketData object
        """
        try:
            # Extract values from Fyers response
            # Note: Fyers API response structure may vary, adjust field names as needed
            ltp = float(data.get("lp", 0))  # Last traded price

            # Handle volume - it might be a dict or a number
            volume_data = data.get("v", 0)
            if isinstance(volume_data, dict):
                volume = int(volume_data.get("volume", 0))
            else:
                volume = int(volume_data) if volume_data else 0

            open_price = float(data.get("o", 0))  # Open
            high = float(data.get("h", 0))  # High
            low = float(data.get("l", 0))  # Low
            close = float(data.get("c", 0))  # Close
            prev_close = float(data.get("prev_close", close))  # Previous close
            
            # Calculate change and change percentage
            change = ltp - prev_close if prev_close > 0 else 0
            change_percent = (change / prev_close * 100) if prev_close > 0 else 0
            
            return MarketData(
                symbol=symbol,
                ltp=ltp,
                volume=volume,
                open_price=open_price,
                high=high,
                low=low,
                close=close,
                prev_close=prev_close,
                change=change,
                change_percent=change_percent
            )
            
        except Exception as e:
            logger.error(f"Error parsing market data for {symbol}: {e}")
            # Return default MarketData with zero values
            return MarketData(
                symbol=symbol,
                ltp=0.0,
                volume=0,
                open_price=0.0,
                high=0.0,
                low=0.0,
                close=0.0,
                prev_close=0.0,
                change=0.0,
                change_percent=0.0
            )
    
    def get_single_quote(self, symbol: str) -> Optional[MarketData]:
        """
        Get market quote for a single symbol.
        
        Args:
            symbol: Symbol string in NSE format
            
        Returns:
            MarketData object or None if failed
        """
        quotes = self.get_quotes([symbol])
        return quotes.get(symbol)
    
    def is_authenticated(self) -> bool:
        """
        Check if client is authenticated.
        
        Returns:
            True if authenticated, False otherwise
        """
        return self.fyers_api is not None and self.access_token is not None
