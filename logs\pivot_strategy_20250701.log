root - INFO - ================================================================================
root - INFO - Starting new logging session
root - INFO - ================================================================================
fyers_config - INFO - ================================================================================
fyers_config - INFO - INDEX SCANNER APPLICATION STARTED
fyers_config - INFO - ================================================================================
fyers_config - INFO - Start time: 2025-07-01 00:15:09
fyers_config - INFO - Loading configuration...
config_loader - INFO - Configuration loaded successfully from C:\Users\<USER>\Desktop\Python\index_scanner\config.yaml
config_loader - INFO - Configuration validation successful
fyers_config - INFO - Configuration loaded and validated successfully
fyers_config - INFO - Target symbols: ['NIFTY', 'BANKNIFTY']
fyers_config - INFO - Volume filter: >= 5
fyers_config - INFO - LTP filter: 2500 - 5000
fyers_config - INFO - Initializing Index Scanner...
fyers_config - INFO - Starting symbol scanning process...
index_scanner - INFO - Starting index scanner...
index_scanner - INFO - Authenticating with Fyers API...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - 
=== Fyers API Authentication ===
fyers_config - INFO - A browser window will open for you to log in to Fyers.
fyers_config - INFO - After logging in, you will be redirected to Google.
fyers_config - INFO - Copy the auth code from the URL and paste it here.
fyers_config - INFO - 
Please login in the private browser window that opened.
fyers_config - INFO - Authentication files saved to C:\Users\<USER>\Desktop\Python\index_scanner
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
index_scanner - INFO - Loading symbols from CSV...
symbol_parser - INFO - Current month expiry (2025-07-31) hasn't passed. Using current + next 2 months.
symbol_parser - INFO - Target months for filtering: ['JUL', 'AUG']
symbol_parser - INFO - Loaded 2334 symbols from CSV for target months
symbol_parser - INFO - Filtered to 2334 symbols for underlyings: ['NIFTY', 'BANKNIFTY']
symbol_parser - INFO - Prepared 2334 symbols for scanning
symbol_parser - INFO - Current month expiry (2025-07-31) hasn't passed. Using current + next 2 months.
symbol_parser - INFO - Target months for filtering: ['JUL', 'AUG']
symbol_parser - INFO - Loaded 2334 symbols from CSV for target months
symbol_parser - INFO - Filtered to 2334 symbols for underlyings: ['NIFTY', 'BANKNIFTY']
symbol_parser - INFO - Saved 2334 filtered symbols to reports\filtered_symbols.csv
index_scanner - INFO - Found 2334 symbols to scan
index_scanner - INFO - Fetching market data from Fyers API...
fyers_client - INFO - Fetching quotes for batch 1: 50 symbols
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL32000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL32000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL39700CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL39700PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL39800CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL39800PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL39900CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL39900PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL70700CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL70700PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL40100CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL40100PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL70300CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL70300PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL70400CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL70400PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL78000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL78000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL29550CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL29550PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL29600CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL29600PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL18800CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL18800PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL18850CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL18850PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL18900CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL18900PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL18950CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL18950PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL70800CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL70800PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL70900CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL70900PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL78500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL78500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL71700CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL71700PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL71800CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL71800PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL79500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL79500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL70600CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL70600PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL30400CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL30400PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL31000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL31000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL31500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL31500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - INFO - Fetching quotes for batch 2: 50 symbols
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL38600CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL38600PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL38700CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL38700PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL38800CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL38800PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL38900CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL38900PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL39100CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL39100PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL39200CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL39200PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL18750CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL18750PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL71100CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL71100PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL71200CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL71200PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL71300CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL71300PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL71400CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL71400PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL71600CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL71600PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL79000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL79000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL29650CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL29650PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL29700CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL29700PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL39300CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL39300PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL39400CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL39400PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL39600CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL39600PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL30350CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL30350PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL29750CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL29750PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL29800CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL29800PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL29850CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL29850PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL29900CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL29900PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL29950CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL29950PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL30000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL30000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - INFO - Fetching quotes for batch 3: 50 symbols
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL30050CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL30050PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL30100CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL30100PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL30150CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL30150PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL30200CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL30200PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL30250CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL30250PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL30300CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL30300PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL32500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL32500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL33000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL33000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL33500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL33500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL34000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL34000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL34500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL34500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL35000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL35000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL35500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL35500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL36000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL36000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL36500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL36500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL37000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL37000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL37500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL37500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL38000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL38000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL38500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL38500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL39000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL39000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL39500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL39500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL40000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL40000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL40200CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL40200PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL40300CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL40300PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL40400CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL40400PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - INFO - Fetching quotes for batch 4: 50 symbols
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL40500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL40500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL40600CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL40600PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL40700CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL40700PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL40800CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL40800PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL40900CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL40900PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL41000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL41000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL41100CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL41100PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL41200CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL41200PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL41300CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL41300PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL41400CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL41400PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL41500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL41500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL41600CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL41600PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL41700CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL41700PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL41800CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL41800PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL41900CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL41900PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL42000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL42000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL42100CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL42100PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL42200CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL42200PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL42300CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL42300PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL42400CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL42400PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL42500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL42500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL42600CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL42600PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL42700CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL42700PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL42800CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL42800PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL42900CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL42900PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - INFO - Fetching quotes for batch 5: 50 symbols
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL43000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL43000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL43100CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL43100PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL43200CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL43200PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL43300CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL43300PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL43400CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL43400PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL43500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL43500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL43600CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL43600PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL43700CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL43700PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL43800CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL43800PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL43900CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL43900PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL44000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL44000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL44100CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL44100PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL44200CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL44200PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL44300CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL44300PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL44400CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL44400PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL44500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL44500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL44600CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL44600PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL44700CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL44700PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL44800CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL44800PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL44900CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL44900PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL45000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL45000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL45100CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL45100PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL45200CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL45200PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL45300CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL45300PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL45400CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL45400PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - INFO - Fetching quotes for batch 6: 50 symbols
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL45500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL45500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL45600CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL45600PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL45700CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL45700PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL45800CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL45800PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL45900CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL45900PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL46000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL46000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL46100CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL46100PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL46200CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL46200PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL46300CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL46300PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL46400CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL46400PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL46500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL46500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL46600CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL46600PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL46700CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL46700PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL46800CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL46800PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL46900CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL46900PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL47000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL47000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL47100CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL47100PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL47200CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL47200PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL47300CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL47300PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL47400CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL47400PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL47500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL47500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL47600CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL47600PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL47700CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL47700PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL47800CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL47800PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL47900CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL47900PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - INFO - Fetching quotes for batch 7: 50 symbols
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL48000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL48000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL48100CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL48100PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL48200CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL48200PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL48300CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL48300PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL48400CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL48400PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL48500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL48500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL48600CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL48600PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL48700CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL48700PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL48800CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL48800PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL48900CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL48900PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL49000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL49000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL49100CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL49100PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL49200CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL49200PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL49300CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL49300PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL49400CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL49400PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL49500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL49500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL49600CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL49600PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL49700CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL49700PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL49800CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL49800PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL49900CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL49900PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL50000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL50000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL50100CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL50100PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL50200CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL50200PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL50300CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL50300PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL50400CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL50400PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - INFO - Fetching quotes for batch 8: 50 symbols
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL50500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL50500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL50600CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL50600PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL50700CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL50700PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL50800CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL50800PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL50900CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL50900PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL51000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL51000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL51100CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL51100PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL51200CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL51200PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL51300CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL51300PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL51400CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL51400PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL51500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL51500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL51600CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL51600PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL51700CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL51700PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL51800CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL51800PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL51900CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL51900PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL52000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL52000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL52100CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL52100PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL52200CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL52200PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL52300CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL52300PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL52400CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL52400PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL52500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL52500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL52600CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL52600PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL52700CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL52700PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL52800CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL52800PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL52900CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL52900PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - INFO - Fetching quotes for batch 9: 50 symbols
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL53000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL53000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL53100CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL53100PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL53200CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL53200PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL53300CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL53300PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL53400CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL53400PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL53500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL53500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL53600CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL53600PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL53700CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL53700PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL53800CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL53800PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL53900CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL53900PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL54000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL54000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL54100CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL54100PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL54200CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL54200PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL54300CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL54300PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL54400CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL54400PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL54500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL54500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL54600CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL54600PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL54700CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL54700PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL54800CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL54800PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL54900CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL54900PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL55000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL55000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL55100CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL55100PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL55200CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL55200PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL55300CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL55300PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL55400CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL55400PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - INFO - Fetching quotes for batch 10: 50 symbols
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL55500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL55500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL55600CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL55600PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL55700CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL55700PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL55800CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL55800PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL55900CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL55900PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL56000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL56000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL56100CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL56100PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL56200CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL56200PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL56300CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL56300PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL56400CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL56400PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL56500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL56500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL56600CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL56600PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL56700CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL56700PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL56800CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL56800PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL56900CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL56900PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL57000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL57000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL57100CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL57100PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL57200CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL57200PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL57300CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL57300PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL57400CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL57400PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL57500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL57500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL57600CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL57600PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL57700CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL57700PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL57800CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL57800PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL57900CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL57900PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - INFO - Fetching quotes for batch 11: 50 symbols
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL58000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL58000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL58100CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL58100PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL58200CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL58200PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL58300CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL58300PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL58400CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL58400PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL58500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL58500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL58600CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL58600PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL58700CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL58700PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL58800CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL58800PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL58900CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL58900PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL59000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL59000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL59100CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL59100PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL59200CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL59200PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL59300CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL59300PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL59400CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL59400PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL59500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL59500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL59600CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL59600PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL59700CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL59700PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL59800CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL59800PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL59900CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL59900PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL60000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL60000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL60100CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL60100PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL60200CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL60200PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL60300CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL60300PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL60400CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL60400PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - INFO - Fetching quotes for batch 12: 50 symbols
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL60500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL60500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL60600CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL60600PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL60700CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL60700PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL60800CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL60800PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL60900CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL60900PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL61000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL61000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL61100CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL61100PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL61200CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL61200PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL61300CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL61300PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL61400CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL61400PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL61500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL61500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL61600CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL61600PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL61700CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL61700PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL61800CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL61800PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL61900CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL61900PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL62000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL62000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL62100CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL62100PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL62200CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL62200PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL62300CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL62300PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL62400CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL62400PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL62500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL62500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL62600CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL62600PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL62700CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL62700PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL62800CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL62800PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL62900CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL62900PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - INFO - Fetching quotes for batch 13: 50 symbols
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL63000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL63000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL63100CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL63100PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL63200CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL63200PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL63300CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL63300PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL63400CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL63400PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL63500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL63500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL63600CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL63600PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL63700CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL63700PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL63800CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL63800PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL63900CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL63900PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL64000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL64000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL64100CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL64100PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL64200CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL64200PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL64300CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL64300PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL64400CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL64400PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL64500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL64500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL64600CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL64600PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL64700CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL64700PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL64800CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL64800PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL64900CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL64900PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL65000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL65000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL65100CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL65100PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL65200CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL65200PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL65300CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL65300PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL65400CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL65400PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - INFO - Fetching quotes for batch 14: 50 symbols
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL65500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL65500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL65600CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL65600PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL65700CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL65700PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL65800CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL65800PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL65900CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL65900PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL66000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL66000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL66100CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL66100PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL66200CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL66200PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL66300CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL66300PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL66400CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL66400PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL66500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL66500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL66600CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL66600PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL66700CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL66700PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL66800CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL66800PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL66900CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL66900PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL67000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL67000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL67100CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL67100PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL67200CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL67200PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL67300CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL67300PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL67400CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL67400PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL67500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL67500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL67600CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL67600PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL67700CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL67700PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL67800CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL67800PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL67900CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL67900PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - INFO - Fetching quotes for batch 15: 50 symbols
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL68000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL68000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL68100CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL68100PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL68200CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL68200PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL68300CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL68300PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL68400CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL68400PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL68500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL68500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL68600CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL68600PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL68700CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL68700PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL68800CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL68800PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL68900CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL68900PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL69000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL69000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL69100CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL69100PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL69200CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL69200PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL69300CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL69300PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL69400CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL69400PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL69500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL69500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL69600CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL69600PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL69700CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL69700PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL69800CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL69800PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL69900CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL69900PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL70000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL70000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL70100CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL70100PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL70200CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL70200PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL70500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL70500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL71000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL71000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - INFO - Fetching quotes for batch 16: 50 symbols
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL71500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL71500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL72000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL72000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL72500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL72500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL73000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL73000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL73500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL73500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL74000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL74000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL74500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL74500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL75000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL75000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL75500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL75500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL76000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL76000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL76500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL76500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL77000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL77000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL77500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25JUL77500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL19000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL19000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL19050CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL19050PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL19100CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL19100PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL19150CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL19150PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL19200CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL19200PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL19250CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL19250PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL19300CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL19300PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL19350CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL19350PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL19400CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL19400PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL19450CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL19450PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL19500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL19500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL19550CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL19550PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - INFO - Fetching quotes for batch 17: 50 symbols
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL19600CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL19600PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL19650CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL19650PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL19700CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL19700PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL19750CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL19750PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL19800CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL19800PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL19850CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL19850PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL19900CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL19900PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL19950CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL19950PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL20000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL20000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL20050CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL20050PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL20100CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL20100PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL20150CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL20150PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL20200CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL20200PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL20250CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL20250PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL20300CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL20300PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL20350CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL20350PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL20400CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL20400PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL20450CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL20450PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL20500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL20500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL20550CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL20550PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL20600CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL20600PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL20650CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL20650PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL20700CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL20700PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL20750CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL20750PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL20800CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL20800PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - INFO - Fetching quotes for batch 18: 50 symbols
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL20850CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL20850PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL20900CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL20900PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL20950CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL20950PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL21000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL21000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL21050CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL21050PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL21100CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL21100PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL21150CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL21150PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL21200CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL21200PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL21250CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL21250PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL21300CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL21300PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL21350CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL21350PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL21400CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL21400PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL21450CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL21450PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL21500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL21500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL21550CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL21550PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL21600CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL21600PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL21650CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL21650PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL21700CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL21700PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL21750CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL21750PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL21800CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL21800PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL21850CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL21850PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL21900CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL21900PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL21950CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL21950PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL22000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL22000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL22050CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL22050PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - INFO - Fetching quotes for batch 19: 50 symbols
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL22100CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL22100PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL22150CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL22150PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL22200CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL22200PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL22250CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL22250PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL22300CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL22300PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL22350CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL22350PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL22400CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL22400PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL22450CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL22450PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL22500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL22500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL22550CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL22550PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL22600CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL22600PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL22650CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL22650PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL22700CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL22700PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL22750CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL22750PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL22800CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL22800PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL22850CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL22850PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL22900CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL22900PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL22950CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL22950PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL23000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL23000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL23050CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL23050PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL23100CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL23100PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL23150CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL23150PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL23200CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL23200PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL23250CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL23250PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL23300CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL23300PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - INFO - Fetching quotes for batch 20: 50 symbols
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL23350CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL23350PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL23400CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL23400PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL23450CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL23450PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL23500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL23500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL23550CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL23550PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL23600CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL23600PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL23650CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL23650PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL23700CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL23700PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL23750CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL23750PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL23800CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL23800PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL23850CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL23850PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL23900CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL23900PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL23950CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL23950PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL24000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL24000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL24050CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL24050PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL24100CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL24100PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL24150CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL24150PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL24200CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL24200PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL24250CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL24250PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL24300CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL24300PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL24350CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL24350PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL24400CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL24400PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL24450CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL24450PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL24500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL24500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL24550CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL24550PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - INFO - Fetching quotes for batch 21: 50 symbols
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL24600CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL24600PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL24650CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL24650PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL24700CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL24700PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL24750CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL24750PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL24800CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL24800PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL24850CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL24850PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL24900CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL24900PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL24950CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL24950PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL25000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL25000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL25050CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL25050PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL25100CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL25100PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL25150CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL25150PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL25200CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL25200PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL25250CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL25250PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL25300CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL25300PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL25350CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL25350PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL25400CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL25400PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL25450CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL25450PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL25500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL25500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL25550CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL25550PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL25600CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL25600PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL25650CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL25650PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL25700CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL25700PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL25750CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL25750PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL25800CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL25800PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - INFO - Fetching quotes for batch 22: 50 symbols
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL25850CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL25850PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL25900CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL25900PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL25950CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL25950PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL26000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL26000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL26050CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL26050PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL26100CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL26100PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL26150CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL26150PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL26200CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL26200PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL26250CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL26250PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL26300CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL26300PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL26350CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL26350PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL26400CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL26400PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL26450CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL26450PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL26500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL26500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL26550CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL26550PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL26600CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL26600PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL26650CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL26650PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL26700CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL26700PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL26750CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL26750PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL26800CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL26800PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL26850CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL26850PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL26900CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL26900PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL26950CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL26950PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL27000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL27000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL27050CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL27050PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - INFO - Fetching quotes for batch 23: 50 symbols
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL27100CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL27100PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL27150CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL27150PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL27200CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL27200PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL27250CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL27250PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL27300CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL27300PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL27350CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL27350PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL27400CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL27400PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL27450CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL27450PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL27500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL27500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL27550CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL27550PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL27600CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL27600PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL27650CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL27650PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL27700CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL27700PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL27750CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL27750PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL27800CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL27800PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL27850CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL27850PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL27900CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL27900PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL27950CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL27950PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL28000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL28000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL28050CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL28050PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL28100CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL28100PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL28150CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL28150PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL28200CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL28200PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL28250CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL28250PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL28300CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL28300PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - INFO - Fetching quotes for batch 24: 50 symbols
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL28350CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL28350PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL28400CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL28400PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL28450CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL28450PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL28500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL28500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL28550CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL28550PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL28600CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL28600PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL28650CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL28650PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL28700CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL28700PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL28750CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL28750PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL28800CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL28800PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL28850CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL28850PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL28900CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL28900PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL28950CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL28950PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL29000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL29000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL29050CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL29050PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL29100CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL29100PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL29150CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL29150PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL29200CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL29200PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL29250CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL29250PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL29300CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL29300PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL29350CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL29350PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL29400CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL29400PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL29450CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL29450PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL29500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25JUL29500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG70600CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG70600PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - INFO - Fetching quotes for batch 25: 50 symbols
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG70700CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG70700PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25AUG19500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25AUG19500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25AUG19550CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25AUG19550PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG70800CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG70800PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG70900CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG70900PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG78500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG78500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25AUG19450CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25AUG19450PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG71700CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG71700PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG71800CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG71800PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG79500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG79500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25AUG30400CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25AUG30400PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25AUG19300CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25AUG19300PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25AUG19350CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25AUG19350PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25AUG19400CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25AUG19400PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG71100CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG71100PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG71200CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG71200PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG71300CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG71300PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG71400CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG71400PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG71600CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG71600PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG79000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG79000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25AUG30150CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25AUG30150PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25AUG30200CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25AUG30200PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25AUG30250CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25AUG30250PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25AUG30300CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25AUG30300PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25AUG30350CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:NIFTY25AUG30350PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - INFO - Fetching quotes for batch 26: 50 symbols
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG33000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG33000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG33500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG33500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG34000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG34000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG34500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG34500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG35000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG35000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG35500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG35500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG36000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG36000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG36500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG36500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG37000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG37000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG37500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG37500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG38000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG38000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG38500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG38500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG39000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG39000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG39500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG39500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG40000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG40000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG40500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG40500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG40600CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG40600PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG40700CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG40700PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG40800CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG40800PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG40900CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG40900PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG41000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG41000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG41100CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG41100PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG41200CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG41200PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG41300CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG41300PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG41400CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG41400PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - INFO - Fetching quotes for batch 27: 50 symbols
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG41500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG41500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG41600CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG41600PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG41700CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG41700PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG41800CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG41800PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG41900CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG41900PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG42000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG42000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG42100CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG42100PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG42200CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG42200PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG42300CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG42300PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG42400CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG42400PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG42500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG42500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG42600CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG42600PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG42700CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG42700PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG42800CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG42800PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG42900CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG42900PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG43000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG43000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG43100CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG43100PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG43200CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG43200PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG43300CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG43300PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG43400CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG43400PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG43500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG43500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG43600CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG43600PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG43700CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG43700PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG43800CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG43800PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG43900CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG43900PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - INFO - Fetching quotes for batch 28: 50 symbols
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG44000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG44000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG44100CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG44100PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG44200CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG44200PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG44300CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG44300PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG44400CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG44400PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG44500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG44500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG44600CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG44600PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG44700CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG44700PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG44800CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG44800PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG44900CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG44900PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG45000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG45000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG45100CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG45100PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG45200CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG45200PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG45300CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG45300PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG45400CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG45400PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG45500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG45500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG45600CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG45600PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG45700CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG45700PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG45800CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG45800PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG45900CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG45900PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG46000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG46000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG46100CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG46100PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG46200CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG46200PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG46300CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG46300PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG46400CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG46400PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - INFO - Fetching quotes for batch 29: 50 symbols
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG46500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG46500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG46600CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG46600PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG46700CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG46700PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG46800CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG46800PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG46900CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG46900PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG47000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG47000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG47100CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG47100PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG47200CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG47200PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG47300CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG47300PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG47400CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG47400PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG47500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG47500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG47600CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG47600PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG47700CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG47700PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG47800CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG47800PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG47900CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG47900PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG48000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG48000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG48100CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG48100PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG48200CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG48200PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG48300CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG48300PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG48400CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG48400PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG48500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG48500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG48600CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG48600PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG48700CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG48700PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG48800CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG48800PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG48900CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG48900PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - INFO - Fetching quotes for batch 30: 50 symbols
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG49000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG49000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG49100CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG49100PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG49200CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG49200PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG49300CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG49300PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG49400CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG49400PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG49500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG49500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG49600CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG49600PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG49700CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG49700PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG49800CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG49800PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG49900CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG49900PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG50000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG50000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG50100CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG50100PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG50200CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG50200PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG50300CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG50300PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG50400CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG50400PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG50500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG50500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG50600CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG50600PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG50700CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG50700PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG50800CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG50800PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG50900CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG50900PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG51000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG51000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG51100CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG51100PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG51200CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG51200PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG51300CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG51300PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG51400CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG51400PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - INFO - Fetching quotes for batch 31: 50 symbols
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG51500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG51500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG51600CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG51600PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG51700CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG51700PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG51800CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG51800PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG51900CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG51900PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG52000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG52000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG52100CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG52100PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG52200CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG52200PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG52300CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG52300PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG52400CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG52400PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG52500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG52500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG52600CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG52600PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG52700CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG52700PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG52800CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG52800PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG52900CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG52900PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG53000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG53000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG53100CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG53100PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG53200CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG53200PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG53300CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG53300PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG53400CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG53400PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG53500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG53500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG53600CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG53600PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG53700CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG53700PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG53800CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG53800PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG53900CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG53900PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - INFO - Fetching quotes for batch 32: 50 symbols
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG54000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG54000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG54100CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG54100PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG54200CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG54200PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG54300CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG54300PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG54400CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG54400PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG54500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG54500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG54600CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG54600PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG54700CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG54700PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG54800CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG54800PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG54900CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG54900PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG55000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG55000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG55100CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG55100PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG55200CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG55200PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG55300CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG55300PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG55400CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG55400PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG55500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG55500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG55600CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG55600PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG55700CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG55700PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG55800CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG55800PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG55900CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG55900PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG56000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG56000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG56100CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG56100PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG56200CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG56200PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG56300CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG56300PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG56400CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG56400PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - INFO - Fetching quotes for batch 33: 50 symbols
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG56500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG56500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG56600CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG56600PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG56700CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG56700PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG56800CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG56800PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG56900CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG56900PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG57000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG57000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG57100CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG57100PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG57200CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG57200PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG57300CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG57300PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG57400CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG57400PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG57500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG57500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG57600CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG57600PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG57700CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG57700PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG57800CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG57800PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG57900CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG57900PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG58000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG58000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG58100CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG58100PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG58200CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG58200PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG58300CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG58300PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG58400CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG58400PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG58500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG58500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG58600CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG58600PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG58700CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG58700PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG58800CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG58800PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG58900CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG58900PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - INFO - Fetching quotes for batch 34: 50 symbols
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG59000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG59000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG59100CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG59100PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG59200CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG59200PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG59300CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG59300PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG59400CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG59400PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG59500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG59500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG59600CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG59600PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG59700CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG59700PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG59800CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG59800PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG59900CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG59900PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG60000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG60000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG60100CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG60100PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG60200CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG60200PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG60300CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG60300PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG60400CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG60400PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG60500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG60500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG60600CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG60600PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG60700CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG60700PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG60800CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG60800PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG60900CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG60900PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG61000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG61000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG61100CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG61100PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG61200CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG61200PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG61300CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG61300PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG61400CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG61400PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - INFO - Fetching quotes for batch 35: 50 symbols
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG61500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG61500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG61600CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG61600PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG61700CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG61700PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG61800CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG61800PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG61900CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG61900PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG62000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG62000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG62100CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG62100PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG62200CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG62200PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG62300CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG62300PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG62400CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG62400PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG62500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG62500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG62600CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG62600PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG62700CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG62700PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG62800CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG62800PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG62900CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG62900PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG63000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG63000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG63100CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG63100PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG63200CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG63200PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG63300CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG63300PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG63400CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG63400PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG63500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG63500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG63600CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG63600PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG63700CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG63700PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG63800CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG63800PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG63900CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG63900PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - INFO - Fetching quotes for batch 36: 50 symbols
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG64000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG64000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG64100CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG64100PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG64200CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG64200PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG64300CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG64300PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG64400CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG64400PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG64500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG64500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG64600CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG64600PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG64700CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG64700PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG64800CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG64800PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG64900CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG64900PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG65000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG65000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG65100CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG65100PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG65200CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG65200PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG65300CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG65300PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG65400CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG65400PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG65500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG65500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG65600CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG65600PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG65700CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG65700PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG65800CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG65800PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG65900CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG65900PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG66000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG66000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG66100CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG66100PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG66200CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG66200PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG66300CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG66300PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG66400CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG66400PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - INFO - Fetching quotes for batch 37: 50 symbols
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG66500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG66500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG66600CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG66600PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG66700CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG66700PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG66800CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG66800PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG66900CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG66900PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG67000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG67000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG67100CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG67100PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG67200CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG67200PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG67300CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG67300PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG67400CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG67400PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG67500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG67500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG67600CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG67600PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG67700CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG67700PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG67800CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG67800PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG67900CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG67900PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG68000CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG68000PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG68100CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG68100PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG68200CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG68200PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG68300CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG68300PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG68400CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG68400PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG68500CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG68500PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG68600CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG68600PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG68700CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG68700PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG68800CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG68800PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG68900CE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - ERROR - Error parsing market data for NSE:BANKNIFTY25AUG68900PE: int() argument must be a string, a bytes-like object or a real number, not 'dict'
fyers_client - INFO - Fetching quotes for batch 38: 50 symbols
fyers_config - INFO - Application interrupted by user
root - INFO - ================================================================================
root - INFO - Starting new logging session
root - INFO - ================================================================================
fyers_config - INFO - ================================================================================
fyers_config - INFO - INDEX SCANNER APPLICATION STARTED
fyers_config - INFO - ================================================================================
fyers_config - INFO - Start time: 2025-07-01 00:17:05
fyers_config - INFO - Loading configuration...
config_loader - INFO - Configuration loaded successfully from C:\Users\<USER>\Desktop\Python\index_scanner\config.yaml
config_loader - INFO - Configuration validation successful
fyers_config - INFO - Configuration loaded and validated successfully
fyers_config - INFO - Target symbols: ['NIFTY', 'BANKNIFTY']
fyers_config - INFO - Volume filter: >= 5
fyers_config - INFO - LTP filter: 2500 - 5000
fyers_config - INFO - Initializing Index Scanner...
fyers_config - INFO - Starting symbol scanning process...
index_scanner - INFO - Starting index scanner...
index_scanner - INFO - Authenticating with Fyers API...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
index_scanner - INFO - Loading symbols from CSV...
symbol_parser - INFO - Current month expiry (2025-07-31) hasn't passed. Using current + next 2 months.
symbol_parser - INFO - Target months for filtering: ['JUL', 'AUG']
symbol_parser - INFO - Loaded 2334 symbols from CSV for target months
symbol_parser - INFO - Filtered to 2334 symbols for underlyings: ['NIFTY', 'BANKNIFTY']
symbol_parser - INFO - Prepared 2334 symbols for scanning
symbol_parser - INFO - Current month expiry (2025-07-31) hasn't passed. Using current + next 2 months.
symbol_parser - INFO - Target months for filtering: ['JUL', 'AUG']
symbol_parser - INFO - Loaded 2334 symbols from CSV for target months
symbol_parser - INFO - Filtered to 2334 symbols for underlyings: ['NIFTY', 'BANKNIFTY']
symbol_parser - INFO - Saved 2334 filtered symbols to reports\filtered_symbols.csv
index_scanner - INFO - Found 2334 symbols to scan
index_scanner - INFO - Fetching market data from Fyers API...
fyers_client - INFO - Fetching quotes for batch 1: 50 symbols
fyers_client - INFO - Fetching quotes for batch 2: 50 symbols
fyers_client - INFO - Fetching quotes for batch 3: 50 symbols
fyers_client - INFO - Fetching quotes for batch 4: 50 symbols
fyers_client - INFO - Fetching quotes for batch 5: 50 symbols
fyers_client - INFO - Fetching quotes for batch 6: 50 symbols
fyers_client - INFO - Fetching quotes for batch 7: 50 symbols
fyers_client - INFO - Fetching quotes for batch 8: 50 symbols
fyers_client - INFO - Fetching quotes for batch 9: 50 symbols
fyers_client - INFO - Fetching quotes for batch 10: 50 symbols
fyers_client - INFO - Fetching quotes for batch 11: 50 symbols
fyers_client - INFO - Fetching quotes for batch 12: 50 symbols
fyers_client - INFO - Fetching quotes for batch 13: 50 symbols
fyers_client - INFO - Fetching quotes for batch 14: 50 symbols
fyers_client - INFO - Fetching quotes for batch 15: 50 symbols
fyers_client - INFO - Fetching quotes for batch 16: 50 symbols
fyers_client - INFO - Fetching quotes for batch 17: 50 symbols
fyers_client - INFO - Fetching quotes for batch 18: 50 symbols
fyers_client - INFO - Fetching quotes for batch 19: 50 symbols
fyers_client - INFO - Fetching quotes for batch 20: 50 symbols
fyers_client - INFO - Fetching quotes for batch 21: 50 symbols
fyers_client - INFO - Fetching quotes for batch 22: 50 symbols
fyers_client - INFO - Fetching quotes for batch 23: 50 symbols
fyers_client - INFO - Fetching quotes for batch 24: 50 symbols
fyers_client - INFO - Fetching quotes for batch 25: 50 symbols
fyers_client - INFO - Fetching quotes for batch 26: 50 symbols
fyers_client - INFO - Fetching quotes for batch 27: 50 symbols
fyers_client - INFO - Fetching quotes for batch 28: 50 symbols
fyers_client - INFO - Fetching quotes for batch 29: 50 symbols
fyers_client - INFO - Fetching quotes for batch 30: 50 symbols
fyers_client - INFO - Fetching quotes for batch 31: 50 symbols
fyers_client - INFO - Fetching quotes for batch 32: 50 symbols
fyers_client - INFO - Fetching quotes for batch 33: 50 symbols
fyers_client - INFO - Fetching quotes for batch 34: 50 symbols
fyers_client - INFO - Fetching quotes for batch 35: 50 symbols
fyers_client - INFO - Fetching quotes for batch 36: 50 symbols
fyers_client - INFO - Fetching quotes for batch 37: 50 symbols
fyers_client - INFO - Fetching quotes for batch 38: 50 symbols
fyers_client - INFO - Fetching quotes for batch 39: 50 symbols
fyers_client - INFO - Fetching quotes for batch 40: 50 symbols
fyers_client - INFO - Fetching quotes for batch 41: 50 symbols
fyers_client - INFO - Fetching quotes for batch 42: 50 symbols
fyers_client - INFO - Fetching quotes for batch 43: 50 symbols
fyers_client - INFO - Fetching quotes for batch 44: 50 symbols
fyers_client - INFO - Fetching quotes for batch 45: 50 symbols
fyers_client - INFO - Fetching quotes for batch 46: 50 symbols
fyers_client - INFO - Fetching quotes for batch 47: 34 symbols
fyers_client - INFO - Successfully fetched market data for 2334 symbols
index_scanner - INFO - Received market data for 2334 symbols
index_scanner - INFO - Applying filters...
index_scanner - INFO - Applying filters - Volume Range: 5-1000000, LTP Range: 2500-5000
index_scanner - INFO - Filtered 0 symbols from 2334 total symbols
index_scanner - INFO - Scanning complete. Found 0 symbols matching criteria
fyers_config - WARNING - No symbols found matching the criteria
root - INFO - ================================================================================
root - INFO - Starting new logging session
root - INFO - ================================================================================
fyers_config - INFO - ================================================================================
fyers_config - INFO - INDEX SCANNER APPLICATION STARTED
fyers_config - INFO - ================================================================================
fyers_config - INFO - Start time: 2025-07-01 00:44:21
fyers_config - INFO - Loading configuration...
config_loader - INFO - Configuration loaded successfully from C:\Users\<USER>\Desktop\Python\index_scanner\config.yaml
config_loader - INFO - Configuration validation successful
fyers_config - INFO - Configuration loaded and validated successfully
fyers_config - INFO - Target symbols: ['NIFTY', 'BANKNIFTY']
fyers_config - INFO - Volume filter: >= 5
fyers_config - INFO - LTP filter: 2500 - 5000
fyers_config - INFO - Initializing Index Scanner...
fyers_config - INFO - Starting symbol scanning process...
index_scanner - INFO - Starting index scanner...
index_scanner - INFO - Authenticating with Fyers API...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
index_scanner - INFO - Loading symbols from CSV...
symbol_parser - INFO - Current month expiry (2025-07-31) hasn't passed. Using current + next 2 months.
symbol_parser - INFO - Target months for filtering: ['JUL', 'AUG']
symbol_parser - INFO - Loaded 2334 symbols from CSV for target months
symbol_parser - INFO - Filtered to 2334 symbols for underlyings: ['NIFTY', 'BANKNIFTY']
symbol_parser - INFO - Prepared 2334 symbols for scanning
symbol_parser - INFO - Current month expiry (2025-07-31) hasn't passed. Using current + next 2 months.
symbol_parser - INFO - Target months for filtering: ['JUL', 'AUG']
symbol_parser - INFO - Loaded 2334 symbols from CSV for target months
symbol_parser - INFO - Filtered to 2334 symbols for underlyings: ['NIFTY', 'BANKNIFTY']
symbol_parser - ERROR - Error saving filtered symbols to CSV: [Errno 13] Permission denied: 'reports\\filtered_symbols.csv'
index_scanner - INFO - Found 2334 symbols to scan
index_scanner - INFO - Fetching market data from Fyers API...
fyers_client - INFO - Fetching quotes for batch 1: 50 symbols
fyers_client - INFO - Fetching quotes for batch 2: 50 symbols
fyers_client - INFO - Fetching quotes for batch 3: 50 symbols
fyers_client - INFO - Fetching quotes for batch 4: 50 symbols
fyers_client - INFO - Fetching quotes for batch 5: 50 symbols
fyers_client - INFO - Fetching quotes for batch 6: 50 symbols
fyers_client - INFO - Fetching quotes for batch 7: 50 symbols
fyers_client - INFO - Fetching quotes for batch 8: 50 symbols
fyers_client - INFO - Fetching quotes for batch 9: 50 symbols
fyers_client - INFO - Fetching quotes for batch 10: 50 symbols
fyers_client - INFO - Fetching quotes for batch 11: 50 symbols
fyers_client - INFO - Fetching quotes for batch 12: 50 symbols
fyers_client - INFO - Fetching quotes for batch 13: 50 symbols
fyers_client - INFO - Fetching quotes for batch 14: 50 symbols
fyers_client - INFO - Fetching quotes for batch 15: 50 symbols
fyers_client - INFO - Fetching quotes for batch 16: 50 symbols
fyers_client - INFO - Fetching quotes for batch 17: 50 symbols
fyers_client - INFO - Fetching quotes for batch 18: 50 symbols
fyers_client - INFO - Fetching quotes for batch 19: 50 symbols
fyers_client - INFO - Fetching quotes for batch 20: 50 symbols
fyers_client - INFO - Fetching quotes for batch 21: 50 symbols
fyers_client - INFO - Fetching quotes for batch 22: 50 symbols
fyers_client - INFO - Fetching quotes for batch 23: 50 symbols
fyers_client - INFO - Fetching quotes for batch 24: 50 symbols
fyers_client - INFO - Fetching quotes for batch 25: 50 symbols
fyers_client - INFO - Fetching quotes for batch 26: 50 symbols
fyers_client - INFO - Fetching quotes for batch 27: 50 symbols
fyers_client - INFO - Fetching quotes for batch 28: 50 symbols
fyers_client - INFO - Fetching quotes for batch 29: 50 symbols
fyers_client - INFO - Fetching quotes for batch 30: 50 symbols
fyers_client - INFO - Fetching quotes for batch 31: 50 symbols
fyers_client - INFO - Fetching quotes for batch 32: 50 symbols
fyers_client - INFO - Fetching quotes for batch 33: 50 symbols
fyers_client - INFO - Fetching quotes for batch 34: 50 symbols
fyers_client - INFO - Fetching quotes for batch 35: 50 symbols
fyers_client - INFO - Fetching quotes for batch 36: 50 symbols
fyers_client - INFO - Fetching quotes for batch 37: 50 symbols
fyers_client - INFO - Fetching quotes for batch 38: 50 symbols
fyers_client - INFO - Fetching quotes for batch 39: 50 symbols
fyers_client - INFO - Fetching quotes for batch 40: 50 symbols
fyers_client - INFO - Fetching quotes for batch 41: 50 symbols
fyers_client - INFO - Fetching quotes for batch 42: 50 symbols
fyers_client - INFO - Fetching quotes for batch 43: 50 symbols
fyers_client - INFO - Fetching quotes for batch 44: 50 symbols
fyers_client - INFO - Fetching quotes for batch 45: 50 symbols
fyers_client - INFO - Fetching quotes for batch 46: 50 symbols
fyers_client - INFO - Fetching quotes for batch 47: 34 symbols
fyers_client - INFO - Successfully fetched market data for 2334 symbols
index_scanner - INFO - Received market data for 2334 symbols
index_scanner - INFO - Applying filters...
index_scanner - INFO - Applying filters - Volume Range: 5-1000000, LTP Range: 2500-5000
index_scanner - INFO - Filtered 0 symbols from 2334 total symbols
index_scanner - INFO - Scanning complete. Found 0 symbols matching criteria
fyers_config - WARNING - No symbols found matching the criteria
root - INFO - ================================================================================
root - INFO - Starting new logging session
root - INFO - ================================================================================
fyers_config - INFO - ================================================================================
fyers_config - INFO - INDEX SCANNER APPLICATION STARTED
fyers_config - INFO - ================================================================================
fyers_config - INFO - Start time: 2025-07-01 00:45:24
fyers_config - INFO - Loading configuration...
config_loader - INFO - Configuration loaded successfully from C:\Users\<USER>\Desktop\Python\index_scanner\config.yaml
config_loader - INFO - Configuration validation successful
fyers_config - INFO - Configuration loaded and validated successfully
fyers_config - INFO - Target symbols: ['NIFTY', 'BANKNIFTY']
fyers_config - INFO - Volume filter: >= 5
fyers_config - INFO - LTP filter: 2500 - 5000
fyers_config - INFO - Initializing Index Scanner...
fyers_config - INFO - Starting symbol scanning process...
index_scanner - INFO - Starting index scanner...
index_scanner - INFO - Authenticating with Fyers API...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
index_scanner - INFO - Loading symbols from CSV...
symbol_parser - INFO - Current month expiry (2025-07-31) hasn't passed. Using current + next 2 months.
symbol_parser - INFO - Target months for filtering: ['JUL', 'AUG']
symbol_parser - INFO - Loaded 2334 symbols from CSV for target months
symbol_parser - INFO - Filtered to 2334 symbols for underlyings: ['NIFTY', 'BANKNIFTY']
symbol_parser - INFO - Prepared 2334 symbols for scanning
symbol_parser - INFO - Current month expiry (2025-07-31) hasn't passed. Using current + next 2 months.
symbol_parser - INFO - Target months for filtering: ['JUL', 'AUG']
symbol_parser - INFO - Loaded 2334 symbols from CSV for target months
symbol_parser - INFO - Filtered to 2334 symbols for underlyings: ['NIFTY', 'BANKNIFTY']
symbol_parser - INFO - Saved 2334 filtered symbols to reports\filtered_symbols.csv
index_scanner - INFO - Found 2334 symbols to scan
index_scanner - INFO - Fetching market data from Fyers API...
fyers_client - INFO - Fetching quotes for batch 1: 50 symbols
fyers_client - INFO - Fetching quotes for batch 2: 50 symbols
fyers_client - INFO - Fetching quotes for batch 3: 50 symbols
fyers_client - INFO - Fetching quotes for batch 4: 50 symbols
fyers_client - INFO - Fetching quotes for batch 5: 50 symbols
fyers_client - INFO - Fetching quotes for batch 6: 50 symbols
fyers_client - INFO - Fetching quotes for batch 7: 50 symbols
fyers_client - INFO - Fetching quotes for batch 8: 50 symbols
fyers_client - INFO - Fetching quotes for batch 9: 50 symbols
fyers_client - INFO - Fetching quotes for batch 10: 50 symbols
fyers_client - INFO - Fetching quotes for batch 11: 50 symbols
fyers_client - INFO - Fetching quotes for batch 12: 50 symbols
fyers_client - INFO - Fetching quotes for batch 13: 50 symbols
fyers_client - INFO - Fetching quotes for batch 14: 50 symbols
fyers_client - INFO - Fetching quotes for batch 15: 50 symbols
fyers_client - INFO - Fetching quotes for batch 16: 50 symbols
fyers_client - INFO - Fetching quotes for batch 17: 50 symbols
fyers_client - INFO - Fetching quotes for batch 18: 50 symbols
fyers_client - INFO - Fetching quotes for batch 19: 50 symbols
fyers_client - INFO - Fetching quotes for batch 20: 50 symbols
fyers_client - INFO - Fetching quotes for batch 21: 50 symbols
fyers_client - INFO - Fetching quotes for batch 22: 50 symbols
fyers_client - INFO - Fetching quotes for batch 23: 50 symbols
fyers_client - INFO - Fetching quotes for batch 24: 50 symbols
fyers_client - INFO - Fetching quotes for batch 25: 50 symbols
fyers_client - INFO - Fetching quotes for batch 26: 50 symbols
fyers_client - INFO - Fetching quotes for batch 27: 50 symbols
fyers_client - INFO - Fetching quotes for batch 28: 50 symbols
fyers_client - INFO - Fetching quotes for batch 29: 50 symbols
fyers_client - INFO - Fetching quotes for batch 30: 50 symbols
fyers_client - INFO - Fetching quotes for batch 31: 50 symbols
fyers_client - INFO - Fetching quotes for batch 32: 50 symbols
fyers_client - INFO - Fetching quotes for batch 33: 50 symbols
fyers_client - INFO - Fetching quotes for batch 34: 50 symbols
fyers_client - INFO - Fetching quotes for batch 35: 50 symbols
fyers_client - INFO - Fetching quotes for batch 36: 50 symbols
fyers_client - INFO - Fetching quotes for batch 37: 50 symbols
fyers_client - INFO - Fetching quotes for batch 38: 50 symbols
fyers_client - INFO - Fetching quotes for batch 39: 50 symbols
fyers_client - INFO - Fetching quotes for batch 40: 50 symbols
fyers_client - INFO - Fetching quotes for batch 41: 50 symbols
fyers_client - INFO - Fetching quotes for batch 42: 50 symbols
fyers_client - INFO - Fetching quotes for batch 43: 50 symbols
fyers_client - INFO - Fetching quotes for batch 44: 50 symbols
fyers_client - INFO - Fetching quotes for batch 45: 50 symbols
fyers_client - INFO - Fetching quotes for batch 46: 50 symbols
fyers_client - INFO - Fetching quotes for batch 47: 34 symbols
fyers_client - INFO - Successfully fetched market data for 2334 symbols
index_scanner - INFO - Received market data for 2334 symbols
index_scanner - INFO - Applying filters...
index_scanner - INFO - Applying filters - Volume Range: 5-1000000, LTP Range: 2500-5000
index_scanner - INFO - Filtered 0 symbols from 2334 total symbols
index_scanner - INFO - Scanning complete. Found 0 symbols matching criteria
fyers_config - WARNING - No symbols found matching the criteria
