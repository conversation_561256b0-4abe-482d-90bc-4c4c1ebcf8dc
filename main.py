"""
Main entry point for the Index Scanner application.
Orchestrates the entire scanning process from configuration loading to report generation.
"""

import logging
import sys
import os
from datetime import datetime

# Import our modules
from fyers_config import setup_logging
from config_loader import get_config
from index_scanner import IndexScanner
from report_generator import ReportGenerator

def main():
    """Main function that orchestrates the index scanning process."""
    
    # Setup logging
    logger = setup_logging(level=logging.INFO, log_to_file=True)
    
    try:
        logger.info("=" * 80)
        logger.info("INDEX SCANNER APPLICATION STARTED")
        logger.info("=" * 80)
        logger.info(f"Start time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Step 1: Load configuration
        logger.info("Loading configuration...")
        config = get_config("config.yaml")
        
        if not config.validate_config():
            logger.error("Configuration validation failed. Please check config.yaml")
            return False
        
        logger.info("Configuration loaded and validated successfully")
        logger.info(f"Target symbols: {config.symbols}")
        logger.info(f"Volume filter: >= {config.min_volume}")
        logger.info(f"LTP filter: {config.min_ltp_price} - {config.max_ltp_price}")
        
        # Step 2: Initialize scanner
        logger.info("Initializing Index Scanner...")
        scanner = IndexScanner(config)
        
        # Step 3: Run the scan
        logger.info("Starting symbol scanning process...")
        filtered_symbols = scanner.scan_symbols()
        
        if not filtered_symbols:
            logger.warning("No symbols found matching the criteria")
            print("\nNo symbols found matching the specified criteria.")
            print("Consider adjusting the filters in config.yaml")
            return True
        
        # Step 4: Generate summary statistics
        logger.info("Generating summary statistics...")
        summary_stats = scanner.get_scan_summary(filtered_symbols)
        
        # Step 5: Generate reports
        logger.info("Generating reports...")
        report_generator = ReportGenerator(output_dir=config.output_dir)
        
        report_files = report_generator.generate_full_report(filtered_symbols, summary_stats)
        
        # Step 6: Log completion
        logger.info("=" * 80)
        logger.info("INDEX SCANNER COMPLETED SUCCESSFULLY")
        logger.info("=" * 80)
        logger.info(f"End time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info(f"CSV Report: {report_files['csv_report']}")
        logger.info(f"Summary Report: {report_files['summary_report']}")
        logger.info(f"Total symbols found: {len(filtered_symbols)}")
        
        # Print final message to console
        print(f"\nScanning completed successfully!")
        print(f"Found {len(filtered_symbols)} symbols matching criteria")
        print(f"Reports saved to: {config.output_dir}")
        print(f"   - CSV Report: {os.path.basename(report_files['csv_report'])}")
        print(f"   - Summary Report: {os.path.basename(report_files['summary_report'])}")
        
        return True
        
    except KeyboardInterrupt:
        logger.info("Application interrupted by user")
        print("\nApplication interrupted by user")
        return False

    except Exception as e:
        logger.error(f"Application failed with error: {e}", exc_info=True)
        print(f"\nApplication failed: {e}")
        print("Check the log files for detailed error information")
        return False

def print_banner():
    """Print application banner."""
    banner = """
    ==============================================================
                         INDEX SCANNER

      Scans NIFTY & BANKNIFTY options for current + next 2 months
      Filters by Volume and LTP based on config.yaml settings

    ==============================================================
    """
    print(banner)

def check_prerequisites():
    """Check if all required files and dependencies are available."""
    required_files = ["config.yaml", "NSE_FO.csv"]
    missing_files = []
    
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print(f"Missing required files: {', '.join(missing_files)}")
        return False

    try:
        # Check if required packages are available
        import yaml
        import fyers_apiv3
        print("All prerequisites satisfied")
        return True
    except ImportError as e:
        print(f"Missing required package: {e}")
        print("Please install required packages using: pip install pyyaml fyers-apiv3")
        return False

if __name__ == "__main__":
    # Print banner
    print_banner()
    
    # Check prerequisites
    if not check_prerequisites():
        sys.exit(1)
    
    # Run main application
    success = main()
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)
