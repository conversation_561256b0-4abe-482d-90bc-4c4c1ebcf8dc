{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-06-30 23:50:52,003+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-06-30 23:50:52,714+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-01 00:15:51,009+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:15:52,340+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:15:52,646+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:15:52,852+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:15:53,057+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:15:53,224+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:15:53,467+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:15:53,655+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:15:53,875+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:15:54,184+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:15:54,341+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:15:54,591+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:15:54,798+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:15:55,002+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:15:55,148+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:15:55,412+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:15:55,617+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:15:55,822+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:15:55,988+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:15:56,296+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:15:56,672+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:15:56,847+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:15:57,050+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:15:57,255+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:15:57,460+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:15:57,595+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:15:57,870+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:15:58,075+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:15:58,279+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:15:58,483+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:15:58,791+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:15:59,215+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:16:00,146+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:16:01,079+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:16:01,711+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:16:02,346+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:16:02,904+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:16:03,719+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-01 00:17:06,431+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:17:07,770+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:17:07,974+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:17:08,115+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:17:08,422+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:17:08,672+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:17:08,822+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:17:09,038+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:17:09,243+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:17:09,450+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:17:09,658+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:17:09,856+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:17:09,964+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:17:10,111+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:17:10,267+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:17:10,471+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:17:10,789+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:17:11,160+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:17:11,761+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:17:12,168+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:17:12,990+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:17:13,563+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:17:13,933+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:17:14,243+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:17:14,504+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:17:14,670+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:17:14,844+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:17:14,976+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:17:15,290+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:17:15,489+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:17:15,787+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:17:16,000+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:17:16,181+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:17:16,307+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:17:16,804+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:17:17,081+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:17:17,858+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:17:18,139+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:17:18,357+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:17:18,496+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:17:18,662+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:17:18,867+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:17:19,072+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:17:19,322+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:17:19,482+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:17:19,668+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:17:19,787+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:17:19,964+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-01 00:44:22,827+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:44:24,154+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:44:24,362+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:44:24,564+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:44:24,769+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:44:25,381+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:44:25,588+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:44:25,793+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:44:25,998+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:44:26,203+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:44:26,406+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:44:26,511+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:44:26,715+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:44:27,022+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:44:27,227+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:44:27,336+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:44:27,627+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:44:27,842+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:44:28,046+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:44:28,308+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:44:28,660+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:44:28,867+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:44:29,173+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:44:29,359+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:44:29,481+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:44:29,685+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:44:29,889+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:44:30,095+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:44:30,299+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:44:30,503+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:44:30,673+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:44:30,914+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:44:31,096+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:44:31,323+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:44:31,529+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:44:31,829+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:44:32,041+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:44:32,551+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:44:32,757+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:44:32,959+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:44:33,166+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:44:33,268+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:44:33,474+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:44:33,678+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:44:33,882+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:44:34,087+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:44:34,293+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:44:34,497+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-01 00:45:25,595+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:45:26,927+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:45:27,102+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:45:27,240+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:45:27,363+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:45:27,541+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:45:27,650+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:45:27,818+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:45:28,052+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:45:28,167+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:45:28,359+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:45:28,565+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:45:28,769+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:45:28,974+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:45:29,091+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:45:29,262+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:45:29,384+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:45:29,690+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:45:29,896+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:45:30,100+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:45:30,304+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:45:30,410+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:45:30,514+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:45:30,697+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:45:30,816+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:45:30,924+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:45:31,125+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:45:31,330+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:45:31,637+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:45:31,753+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:45:31,944+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:45:32,103+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:45:32,355+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:45:32,459+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:45:32,662+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:45:32,865+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:45:33,070+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:45:33,188+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:45:33,480+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:45:33,685+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:45:33,890+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:45:34,095+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:45:34,411+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:45:34,709+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:45:34,923+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:45:35,120+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:45:35,323+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:139] fyersModel","message":{"Status Code":200,"API":"/quotes"},"timestamp":"2025-07-01 00:45:35,527+0530","service":"FyersAPIRequest"}
